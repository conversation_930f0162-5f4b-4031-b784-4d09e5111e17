<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.forum.dao.PostFavoriteDao">
    <!-- 结果映射 -->
    <resultMap id="postFavoriteResultMap" type="PostFavorite">
        <id property="id" column="id"/>
        <result property="postId" column="post_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    
    <!-- 根据帖子ID和用户ID获取收藏记录 -->
    <select id="getPostFavorite" resultMap="postFavoriteResultMap">
        SELECT id, post_id, user_id, create_time
        FROM post_favorite
        WHERE post_id = #{postId} AND user_id = #{userId}
    </select>
    
    <!-- 插入新收藏记录 -->
    <insert id="insertPostFavorite" parameterType="PostFavorite" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO post_favorite (post_id, user_id, create_time)
        VALUES (#{postId}, #{userId}, #{createTime})
    </insert>
    
    <!-- 删除收藏记录 -->
    <delete id="deletePostFavorite">
        DELETE FROM post_favorite
        WHERE post_id = #{postId} AND user_id = #{userId}
    </delete>
    
    <!-- 根据帖子ID获取收藏列表 -->
    <select id="getPostFavoritesByPostId" resultMap="postFavoriteResultMap" parameterType="long">
        SELECT id, post_id, user_id, create_time
        FROM post_favorite
        WHERE post_id = #{postId}
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据用户ID获取收藏列表（分页） -->
    <select id="getPostFavoritesByUserId" resultMap="postFavoriteResultMap">
        SELECT id, post_id, user_id, create_time
        FROM post_favorite
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 获取帖子收藏总数 -->
    <select id="getPostFavoriteCountByPostId" resultType="int" parameterType="long">
        SELECT COUNT(*)
        FROM post_favorite
        WHERE post_id = #{postId}
    </select>
    
    <!-- 获取用户收藏总数 -->
    <select id="getPostFavoriteCountByUserId" resultType="int" parameterType="long">
        SELECT COUNT(*)
        FROM post_favorite
        WHERE user_id = #{userId}
    </select>
</mapper> 