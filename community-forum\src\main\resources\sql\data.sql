-- Use database
USE community_forum;

-- Insert sample users
-- Password is '123456' encrypted with BCrypt
INSERT INTO `user` (`username`, `password`, `email`, `nickname`, `avatar`, `bio`, `status`, `role`, `create_time`, `update_time`) 
VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '<EMAIL>', 'Admin', '/static/images/avatar/admin.jpg', 'System administrator', 1, 'ADMIN', NOW(), NOW()),
('user1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '<EMAIL>', 'User One', '/static/images/avatar/user1.jpg', 'I am user one', 1, 'USER', NOW(), NOW()),
('user2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '<EMAIL>', 'User Two', '/static/images/avatar/user2.jpg', 'I am user two', 1, 'USER', NOW(), NOW());

-- Insert sample categories
INSERT INTO `category` (`name`, `description`, `icon`, `sort_order`, `status`, `create_time`, `update_time`) 
VALUES 
('Announcements', 'Official announcements from administrators', 'fa-bullhorn', 1, 1, NOW(), NOW()),
('General Discussion', 'General topics for discussion', 'fa-comments', 2, 1, NOW(), NOW()),
('Technology', 'Technology related discussions', 'fa-laptop', 3, 1, NOW(), NOW()),
('Help & Support', 'Get help from the community', 'fa-life-ring', 4, 1, NOW(), NOW());

-- Insert sample posts
INSERT INTO `post` (`title`, `content`, `user_id`, `category_id`, `view_count`, `like_count`, `comment_count`, `status`, `create_time`, `update_time`) 
VALUES 
('Welcome to our community forum!', '<p>Welcome to our new community forum! This is a place where you can discuss various topics, share your thoughts, and connect with other members.</p><p>Please make sure to follow our community guidelines and be respectful to others.</p>', 1, 1, 120, 15, 5, 2, NOW(), NOW()),
('How to use this forum?', '<p>Here are some tips on how to use this forum effectively:</p><ul><li>Use the search function to find existing topics</li><li>Choose the appropriate category for your posts</li><li>Be clear and concise in your posts</li><li>Use formatting to make your posts more readable</li></ul>', 1, 1, 85, 10, 3, 1, NOW(), NOW()),
('Introducing myself', '<p>Hello everyone! I am new here and wanted to introduce myself. I am interested in technology, programming, and design.</p><p>Looking forward to engaging with this community!</p>', 2, 2, 42, 7, 2, 1, NOW(), NOW()),
('What programming language should I learn first?', '<p>I am a beginner in programming and would like to know which programming language would be best to learn first. I am interested in web development.</p><p>Any suggestions would be appreciated!</p>', 3, 3, 65, 3, 4, 1, NOW(), NOW());

-- Insert sample comments
INSERT INTO `comment` (`content`, `post_id`, `user_id`, `parent_id`, `to_user_id`, `status`, `create_time`, `update_time`) 
VALUES 
('Thank you for the warm welcome!', 1, 2, NULL, NULL, 1, NOW(), NOW()),
('This is very helpful information.', 2, 3, NULL, NULL, 1, NOW(), NOW()),
('Welcome to the community! Feel free to ask any questions.', 3, 1, NULL, NULL, 1, NOW(), NOW()),
('I would recommend starting with JavaScript for web development.', 4, 1, NULL, NULL, 1, NOW(), NOW()),
('I agree with the admin. JavaScript is a good starting point.', 4, 2, 4, 1, 1, NOW(), NOW());

-- Insert sample likes
INSERT INTO `post_like` (`post_id`, `user_id`, `create_time`) 
VALUES 
(1, 2, NOW()),
(1, 3, NOW()),
(2, 2, NOW()),
(3, 1, NOW()),
(4, 1, NOW());

-- Insert sample favorites
INSERT INTO `post_favorite` (`post_id`, `user_id`, `create_time`) 
VALUES 
(1, 3, NOW()),
(2, 3, NOW()),
(3, 1, NOW());

-- Insert sample notifications
INSERT INTO `notification` (`type`, `sender_id`, `receiver_id`, `post_id`, `comment_id`, `content`, `is_read`, `create_time`) 
VALUES 
('COMMENT', 2, 1, 1, 1, 'User One commented on your post "Welcome to our community forum!"', 0, NOW()),
('LIKE', 2, 1, 1, NULL, 'User One liked your post "Welcome to our community forum!"', 0, NOW()),
('SYSTEM', NULL, 1, NULL, NULL, 'Welcome to the Community Forum! Please complete your profile.', 0, NOW()),
('COMMENT', 1, 3, 4, 4, 'Admin commented on your post "What programming language should I learn first?"', 0, NOW()); 