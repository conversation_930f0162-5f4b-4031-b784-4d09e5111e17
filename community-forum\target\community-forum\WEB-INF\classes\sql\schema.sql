-- Create database
CREATE DATABASE IF NOT EXISTS community_forum DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Use database
USE community_forum;

-- User table
CREATE TABLE IF NOT EXISTS `user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'User ID',
  `username` varchar(50) NOT NULL COMMENT 'Username',
  `password` varchar(100) NOT NULL COMMENT 'Password (encrypted)',
  `email` varchar(100) NOT NULL COMMENT 'Email address',
  `avatar` varchar(255) DEFAULT NULL COMMENT 'Avatar URL',
  `nickname` varchar(50) DEFAULT NULL COMMENT 'Nickname',
  `bio` varchar(255) DEFAULT NULL COMMENT 'User bio',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'User status: 0-disabled, 1-enabled',
  `role` varchar(20) NOT NULL DEFAULT 'USER' COMMENT 'User role: US<PERSON>, ADMI<PERSON>',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User table';

-- Category table
CREATE TABLE IF NOT EXISTS `category` (
  `category_id` int NOT NULL AUTO_INCREMENT COMMENT 'Category ID',
  `name` varchar(50) NOT NULL COMMENT 'Category name',
  `description` varchar(255) DEFAULT NULL COMMENT 'Category description',
  `icon` varchar(255) DEFAULT NULL COMMENT 'Category icon',
  `sort_order` int DEFAULT '0' COMMENT 'Sort order',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'Status: 0-disabled, 1-enabled',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Category table';

-- Post table
CREATE TABLE IF NOT EXISTS `post` (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Post ID',
  `title` varchar(100) NOT NULL COMMENT 'Post title',
  `content` text NOT NULL COMMENT 'Post content',
  `user_id` bigint NOT NULL COMMENT 'Author user ID',
  `category_id` int NOT NULL COMMENT 'Category ID',
  `view_count` int DEFAULT '0' COMMENT 'View count',
  `like_count` int DEFAULT '0' COMMENT 'Like count',
  `comment_count` int DEFAULT '0' COMMENT 'Comment count',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'Status: 0-deleted, 1-normal, 2-pinned',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Post table';

-- Comment table
CREATE TABLE IF NOT EXISTS `comment` (
  `comment_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Comment ID',
  `content` varchar(1000) NOT NULL COMMENT 'Comment content',
  `post_id` bigint NOT NULL COMMENT 'Post ID',
  `user_id` bigint NOT NULL COMMENT 'User ID',
  `parent_id` bigint DEFAULT NULL COMMENT 'Parent comment ID, NULL if top-level comment',
  `to_user_id` bigint DEFAULT NULL COMMENT 'Reply to user ID, NULL if not a reply',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'Status: 0-deleted, 1-normal',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`comment_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Comment table';

-- Like table (for posts)
CREATE TABLE IF NOT EXISTS `post_like` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `post_id` bigint NOT NULL COMMENT 'Post ID',
  `user_id` bigint NOT NULL COMMENT 'User ID',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_post_user` (`post_id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Post like table';

-- Favorite table (for posts)
CREATE TABLE IF NOT EXISTS `post_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `post_id` bigint NOT NULL COMMENT 'Post ID',
  `user_id` bigint NOT NULL COMMENT 'User ID',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_post_user` (`post_id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Post favorite table';

-- Notification table
CREATE TABLE IF NOT EXISTS `notification` (
  `notification_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Notification ID',
  `type` varchar(20) NOT NULL COMMENT 'Notification type: COMMENT, LIKE, FAVORITE, SYSTEM',
  `sender_id` bigint DEFAULT NULL COMMENT 'Sender user ID, NULL if system notification',
  `receiver_id` bigint NOT NULL COMMENT 'Receiver user ID',
  `post_id` bigint DEFAULT NULL COMMENT 'Related post ID',
  `comment_id` bigint DEFAULT NULL COMMENT 'Related comment ID',
  `content` varchar(255) DEFAULT NULL COMMENT 'Notification content',
  `is_read` tinyint NOT NULL DEFAULT '0' COMMENT 'Read status: 0-unread, 1-read',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  PRIMARY KEY (`notification_id`),
  KEY `idx_receiver_id` (`receiver_id`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Notification table'; 